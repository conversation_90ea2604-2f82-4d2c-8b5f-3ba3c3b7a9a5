/**
 * SIM Management JavaScript Module
 * Handles SIM card selection and management functionality
 */

// Global variables for SIM management
var g_currentSimCard = 0;  // Current selected SIM card (0=SIM1, 1=SIM2)
var g_simCardStatus = []; // Array to store SIM card status
var g_simManageXmlName = 'sim_manage';

/**
 * SIM Management Object
 */
(function($) {
    $.fn.objSimManage = function(InIt) {
        var xmlName = g_simManageXmlName;
        var controlMapExisting = new Array(0);
        var controlMapCurrent = new Array(0);

        this.onLoad = function() {
            this.loadHTML();
            document.getElementById("title").innerHTML = jQuery.i18n.prop(InIt);
            var arrayLabels = document.getElementsByTagName("label");
            lableLocaliztion(arrayLabels);
            
            // Load SIM management data
            this.loadSimData();
        }

        this.loadSimData = function() {
            var xml = getData(xmlName);
            if (xml) {
                $(xml).find("sim_manage").each(function() {
                    g_currentSimCard = parseInt($(this).find("sim_card").text()) || 0;
                    var isVerify = $(this).find("is_verify").text();
                    var simPassword = $(this).find("sim_password").text();
                    
                    // Update UI with current SIM card selection
                    updateSimCardSelection(g_currentSimCard);
                });
            }
        }

        this.onPost = function() {
            if (this.isValid()) {
                var _controlMap = this.getPostData();
                if (_controlMap.length > 0) {
                    PostXMLWithResponse(xmlName, g_objXML.getXMLDocToString(g_objXML.createXML(_controlMap)), 
                        function(response) {
                            // Handle response
                            if (response) {
                                showMsgBox(jQuery.i18n.prop("lSuccess"), jQuery.i18n.prop("lSimCardSwitchSuccess"));
                                // Reload dashboard to reflect changes
                                setTimeout(function() {
                                    if (typeof g_objContent !== 'undefined' && g_objContent.onLoad) {
                                        g_objContent.onLoad(true);
                                    }
                                }, 1000);
                            } else {
                                showMsgBox(jQuery.i18n.prop("lError"), jQuery.i18n.prop("lSimCardSwitchFailed"));
                            }
                        });
                }
            }
        }

        this.isValid = function() {
            return true; // Add validation logic if needed
        }

        this.getPostData = function() {
            var index = 0;
            var mapData = new Array(0);
            
            // Get selected SIM card value
            var selectedSim = getSelectedSimCard();
            if (selectedSim !== g_currentSimCard) {
                mapData = g_objXML.putMapElement(mapData, "RGW/sim_manage/sim_card", selectedSim.toString(), index++);
                mapData = g_objXML.putMapElement(mapData, "RGW/sim_manage/is_verify", "0", index++);
            }
            
            return mapData;
        }

        this.setXMLName = function(_xmlname) {
            xmlName = _xmlname;
        }

        this.loadHTML = function() {
            // This would load HTML if we had a dedicated SIM management page
            // For now, we'll integrate into dashboard
        }

        return this.each(function() {
            // Initialization code
        });
    }
})(jQuery);

/**
 * Get current SIM card status from WAN data
 */
function getSimCardStatus() {
    var simStatus = [];
    var xml = callProductXML("wan");
    
    if (xml) {
        // Get SIM1 status
        var sim1Status = $(xml).find("sim_status").text();
        var sim1Present = (sim1Status === "0"); // 0 = present, 1 = absent
        
        simStatus.push({
            slot: 0,
            present: sim1Present,
            status: sim1Status,
            label: jQuery.i18n.prop("lSim1Card")
        });

        // For dual SIM devices, check SIM2 status
        // This would need to be implemented based on actual hardware support
        var sim2Present = false; // Default to false, implement actual detection
        simStatus.push({
            slot: 1,
            present: sim2Present,
            status: "1", // Assume absent for now
            label: jQuery.i18n.prop("lSim2Card")
        });
    }
    
    g_simCardStatus = simStatus;
    return simStatus;
}

/**
 * Create SIM card selection dropdown HTML
 */
function createSimCardDropdown() {
    var simStatus = getSimCardStatus();
    var html = '<strong id="pSimCardSelection">' + jQuery.i18n.prop("pSimCardSelection") + '</strong><br/>';
    html += '<select id="simCardDropdown" onchange="onSimCardSelectionChange()">';
    
    for (var i = 0; i < simStatus.length; i++) {
        var sim = simStatus[i];
        var selected = (sim.slot === g_currentSimCard) ? ' selected' : '';
        var disabled = !sim.present ? ' disabled' : '';
        var statusText = sim.present ? jQuery.i18n.prop("lSIMPresent") : jQuery.i18n.prop("lSIMAbsent");
        
        html += '<option value="' + sim.slot + '"' + selected + disabled + '>';
        html += sim.label + ' (' + statusText + ')';
        html += '</option>';
    }
    
    html += '</select><br/><br/>';
    return html;
}

/**
 * Get selected SIM card from dropdown
 */
function getSelectedSimCard() {
    var dropdown = document.getElementById("simCardDropdown");
    return dropdown ? parseInt(dropdown.value) : g_currentSimCard;
}

/**
 * Handle SIM card selection change
 */
function onSimCardSelectionChange() {
    var selectedSim = getSelectedSimCard();
    if (selectedSim !== g_currentSimCard) {
        // Show confirmation dialog
        var confirmMsg = jQuery.i18n.prop("lSimCardSwitchConfirm").replace("{0}", 
            jQuery.i18n.prop(selectedSim === 0 ? "lSim1Card" : "lSim2Card"));
        
        if (confirm(confirmMsg)) {
            switchSimCard(selectedSim);
        } else {
            // Revert selection
            updateSimCardSelection(g_currentSimCard);
        }
    }
}

/**
 * Switch to selected SIM card
 */
function switchSimCard(simSlot) {
    var mapData = new Array(0);
    var index = 0;
    
    mapData = g_objXML.putMapElement(mapData, "RGW/sim_manage/sim_card", simSlot.toString(), index++);
    mapData = g_objXML.putMapElement(mapData, "RGW/sim_manage/is_verify", "0", index++);
    
    PostXMLWithResponse(g_simManageXmlName, g_objXML.getXMLDocToString(g_objXML.createXML(mapData)), 
        function(response) {
            if (response) {
                g_currentSimCard = simSlot;
                showMsgBox(jQuery.i18n.prop("lSuccess"), jQuery.i18n.prop("lSimCardSwitchSuccess"));
                
                // Reload dashboard after a short delay
                setTimeout(function() {
                    if (typeof objdashboard !== 'undefined' && objdashboard.onLoad) {
                        objdashboard.onLoad(true);
                    }
                }, 2000);
            } else {
                showMsgBox(jQuery.i18n.prop("lError"), jQuery.i18n.prop("lSimCardSwitchFailed"));
                updateSimCardSelection(g_currentSimCard); // Revert selection
            }
        });
}

/**
 * Update SIM card dropdown selection
 */
function updateSimCardSelection(simSlot) {
    var dropdown = document.getElementById("simCardDropdown");
    if (dropdown) {
        dropdown.value = simSlot;
    }
}

/**
 * Initialize SIM management on page load
 */
function initSimManagement() {
    // Get current SIM card status from sim_manage XML
    var xml = getData(g_simManageXmlName);
    if (xml) {
        $(xml).find("sim_manage").each(function() {
            g_currentSimCard = parseInt($(this).find("sim_card").text()) || 0;
        });
    }
    
    // Update SIM card status
    getSimCardStatus();
}

// Initialize when document is ready
$(document).ready(function() {
    initSimManagement();
});
