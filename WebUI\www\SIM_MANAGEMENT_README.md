# SIM 卡管理功能实现说明

## 功能概述
在前端 WebUI 的 Dashboard 页面中添加了 SIM 卡选择功能，用户可以在网络连接部分看到 SIM 卡选择下拉框，并能够切换不同的 SIM 卡。

## 实现的文件修改

### 1. 新增文件
- `WebUI\www\js\panel\router\sim_manage.js` - SIM 卡管理 JavaScript 模块

### 2. 修改的文件
- `WebUI\www\js\panel\dashboard.js` - 在 Cellular 部分添加 SIM 卡选择下拉框
- `WebUI\www\index.html` - 引入 sim_manage.js 文件
- `WebUI\www\properties\Messages_en.properties` - 添加英文国际化文本
- `WebUI\www\properties\Messages_cn.properties` - 添加中文国际化文本

## 功能特性

### 1. SIM 卡状态显示
- 显示当前选中的 SIM 卡
- 显示每个 SIM 卡的状态（存在/不存在）
- 禁用不可用的 SIM 卡选项

### 2. SIM 卡切换
- 用户选择不同 SIM 卡时显示确认对话框
- 通过后端 `sim_manage` 模块进行 SIM 卡切换
- 切换成功后自动刷新 Dashboard 显示

### 3. 界面集成
- SIM 卡选择框显示在信号强度上方
- 在所有 Cellular 连接状态下都显示（有卡、无卡、PIN 锁定等）
- 支持中英文界面

## 后端接口

### XML 模块
- 使用 `sim_manage` XML 模块
- 设置参数：
  - `sim_card`: SIM 卡槽位 (0=SIM1, 1=SIM2)
  - `is_verify`: 验证标志 (设为 0)

### 后端处理函数
- `sim_manage_post_set()` - 处理 SIM 卡设置
- `sim_manage_post_get()` - 获取 SIM 卡状态

## 国际化文本

### 英文 (Messages_en.properties)
```
pSimCardSelection = SIM Card Selection:
lSim1Card = SIM Card 1
lSim2Card = SIM Card 2
lSimCardSwitchConfirm = Are you sure you want to switch to {0}? This may cause a temporary disconnection.
lSimCardSwitchSuccess = SIM card switched successfully.
lSimCardSwitchFailed = Failed to switch SIM card. Please try again.
```

### 中文 (Messages_cn.properties)
```
pSimCardSelection = SIM卡选择:
lSim1Card = SIM卡1
lSim2Card = SIM卡2
lSimCardSwitchConfirm = 确定要切换到{0}吗？这可能会导致暂时断网。
lSimCardSwitchSuccess = SIM卡切换成功。
lSimCardSwitchFailed = SIM卡切换失败，请重试。
```

## 测试要点

### 1. 界面显示测试
- [ ] Dashboard 页面正确显示 SIM 卡选择下拉框
- [ ] 下拉框显示在信号强度上方
- [ ] 中英文界面切换正常
- [ ] 不同 SIM 状态下都能正确显示

### 2. 功能测试
- [ ] 能够正确获取当前 SIM 卡状态
- [ ] 选择不同 SIM 卡时显示确认对话框
- [ ] 确认后能够发送正确的 XML 请求到后端
- [ ] 切换成功后页面自动刷新

### 3. 错误处理测试
- [ ] 后端返回错误时显示错误消息
- [ ] 取消切换时恢复原选择
- [ ] 网络错误时的处理

## 注意事项

1. **硬件支持**: 当前实现假设支持双 SIM 卡，实际部署时需要根据硬件情况调整
2. **SIM2 检测**: 目前 SIM2 状态检测需要根据实际硬件实现
3. **权限控制**: 可能需要添加用户权限检查
4. **状态同步**: 确保前后端 SIM 卡状态保持同步

## 扩展建议

1. 添加 SIM 卡信息显示（IMSI、运营商等）
2. 支持更多 SIM 卡槽位
3. 添加 SIM 卡热插拔检测
4. 增加 SIM 卡使用统计功能
