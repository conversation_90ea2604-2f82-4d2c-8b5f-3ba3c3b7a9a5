btUpdate = 保存
btnUpdate = 更新
btRun = 运行
btnExit = 退出安装
btnNext = 下一步
btnBack = 上一步
btnExit1 = 退出安装
btnNext1 = 下一步
btnBack1 = 上一步
btnExit3 = 退出安装
btnNext3 = 下一步
btnBack3 = 上一步
btnBack4 = 上一步
helpName = 帮助
lKickOffUser = 其他客户端登陆导致强制退出系统
quickSetupName = 快速设置
LogOutName = 退出
lableWelcome = 欢迎
# lTitle = ASR 无线路由器 UAPX-88
# lTitleUAPXB =  ASR 无线路由器 UAPXB-88
# lTitleUAPXC =  ASR 无线路由器 UAPXC-88
# lTitleAPX = ASR 无线路由器 APX-88
# lTitleShareFile = ASR 无线路由器 APX-88 , 共享SD卡文件
lTitle = 无线路由器 UAPX-88
lTitleUAPXB =  无线路由器 UAPXB-88
# Begin Add by ycc, modify locale
# lTitleUAPXC =  无线路由器 UAPXC-88
lTitleUAPXC = 无线路由器 XY695
# End Add by ycc, modify locale
lTitleAPX = 无线路由器 APX-88
lTitleShareFile = 无线路由器 APX-88 , 共享SD卡文件
lloginfailed = 无效的用户名或密码
lnoconn = 连接错误
laUsername = 用户名
laPassword = 密码
btnSignIn = 登录
btnSharedFile = 共享文件
# Begin Add by ycc, modify locale
# lacopyright = 版权 (C) 2017. ASR Limited.版权所有.
lacopyright =
# End Add by ycc, modify locale
lEnabled = 启用
lDisabled = 禁用
lAllow = 允许
lDeny = 拒绝
lVisible = 可见的
lInvisible = 不可见的
lAlways = 自动
lManual = 手动
lOnDemand = 按需求
lFull = 无限制的
lRestricted = 限制的
lAscii = Ascii
lHex = Hex
lIAccess = 访问以太网
lUSBAccess = 访问U盘
ErrTimeZoneNotFound = 非有效时区
ErrInvalidName = 设备名称不能包括这些字符 # , : , ' '(空格), & , ; , ~ , | , < , > , $.
btnOk = 确认
btnOK = 确认
btnOK_PDP = 确认
btnCancel = 取消
btnTriggerOk1 = 确认
btnTriggerCancel = 取消
tableNoData = 无记录.
XMLExtError = 仅XML文件可被上传。
BinExtError = 仅支持上传bin文件格式
lSignIn = 登录
btnTriggerOk = 确定
h1PleaseWait = 请稍候...
# Begin Add by ycc, modify locale
# lAlert = Marvell无线路由器
lAlert = 无线路由器
# End Add by ycc, modify locale
lErrorPost = 数据传输错误
lIPSettingInvalid = IP地址设定非法,必须是0—255
MAC_ADDR_IS_EMPTY = MAC地址不能为空
MAC_ADDR_EXIST = 相同的MAC地址已经存在
IP_ADDR_IS_EMPTY = IP地址不能为空
lMacIpExist = MAC地址或IP地址已经存在
lEmptyName = 名字不能为空
lRuleNameIsChinese=规则名称不支持中文字符



#User management
lUsername = 路由器用户名 :
lPassword = 路由器密码 :
lRePassword = 重新输入密码 :
lPassErrorMes = 密码不匹配
lminLengthError = 用户名和密码必须分4个字符
ErrInvalidUserPass = 用户名和密码不能包含 ! , @ , # , : , ' '(space), & , ; , ~ , | , < , > , $.
btnAddNewAccount = 增加新的账户
lAccountTabletitle = 账户信息
ltAccountName = 账户名
ltAccountGroup = 访问权限
ltAccountDelColumn = 操作
h1AccountEdit = 账户信息
lAccountName = 路由器用户名:
lAccountPassword = 路由器密码:
lReAccountPassword = 重新输入密码:
lAccountAuthority = 访问权限
opt_standard = 标准
opt_restricted = 受限
lStandard = 标准
lRestricted = 受限
btnOk = 确定
lRemove = 删除
lMaxAccountError = 最多允许4个账户
opt_restricted = 受限
opt_standard = 标准
lAccountExist = 用户名已存在

#Wireless Network Settings
lWNS = 无线网络设置
lWN = 状态:
l80211_mode = 802.11模式 :
lChannel = 主信道 :
lBandwidth = 信道带宽 :
lMaxClients = 最大同时连接数:
lSleepTimeSetting= 无线网络自动关闭时间间隔设置
lShowAdvancedSetting = 高级设置
lothernw = 选择无线网络 :
sSleepTimeUint = 分钟（10-60）
dropdown80211n = 802.11n(b/g 兼容)
dropdown80211bg = 802.11 b/g
dropdown80211b_only = 仅802.11 b
dropdown80211g_only = 仅802.11 g
dropdownWirelessAuto = 自动
dropdownCH1 = 信道 1 - 2412 MHz
dropdownCH2 = 信道 2 - 2417 MHz
dropdownCH3 = 信道 3 - 2422 MHz
dropdownCH4 = 信道 4 - 2427 MHz
dropdownCH5 = 信道 5 - 2432 MHz
dropdownCH6 = 信道 6 - 2437 MHz
dropdownCH7 = 信道 7 - 2442 MHz
dropdownCH8 = 信道 8 - 2447 MHz
dropdownCH9 = 信道 9 - 2452 MHz
dropdownCH10 = 信道 10 - 2457 MHz
dropdownCH11 = 信道 11 - 2462 MHz
dropdownCH12 = 信道 12 - 2467 MHz
dropdownCH13 = 信道 13 - 2472 MHz
dropdownBWAuto = 自动 (20/40 MHz)
dropdownBW20 = 20 MHz
dropdownBW40 = 40 MHz
lWifiAutoOffFun = 无线网络自动关闭功能
lDisableWifiAutoOff = 禁用无线网络自动关闭功能
lBand40AcsSwitchEnable = Band40 ACS 切换
WifiModeInvalideTip = WEP加密模式不支持802.11n，请先修改加密模式。
l80211_mode_50G = 802.11模式 :
lrfband = 波段 :
lBeaconPeriodSetting = Beacon Period设置
lBeaconPeriodErrorLogs = Beacon Period只能是50到4000之间的数字
lSleepTimeErrorLogs = 休眠时间只能是10到60之间的数字
lDTIMIntervalSetting = DTIM Interval设置
lDTIMIntervalErrorLogs = DTIM Interval只能是1到100之间的数字
lMaxClientErrorLogs = 最大客户端连接数只能是数字1到
sBeaconPeriod = (毫秒(50~4000))
lChannelChoose = 次信道
ApIsolateSwitchLabel = AP隔离开关
EnableApIsolateSwitch = 打开
DisabledApIsolateSwitch = 关闭

#Time Setting
lYear = 年\月\日:
lMonth = 月:
lDay = 日:
lHour = 时:分:秒(24H):
lMinute = 分:
lSecond = 秒:
lManualTimeSetting = 系统启动设置
btUpdate_time = 保存
btSaveDateTime = 保存当前时间
pwaitUpdatetime = 正在更新时间，请等待
pSuccesscompleteupdateTime = 时间更新完毕，请确认
pFailedcompleteupdateTime = 时间更新失败
pUpdateTimeOut = 更新网络时间操作超时，请重新操作
btUpdateNTPtime = 立即更新为网络时间
lSyncupwithNtpTimeTip = 系统启动时自动更新为网络时间
lTimeErrorMes = 日期或者时间格式不正确
lEmptyNTPServerIP = 网络时间协议服务器IP为空
lEmptyYear = 年为空
lEmptyMonth = 月为空
lEmptyDay = 日为空
lEmptyHour = 时为空
lEmptyMinute = 分为空
lEmptySecond = 秒为空
lYearNumErr = 年只能填入数字
lYearLenErr = 年数字的长度小于5位
lMonthNumErr = 月只能填入数字
lMonthLenErr = 月数字只能是1-12
lDayNumErr = 日只能填入数字
lDayRangeLeap = 闰年的2月日期只能是1-29
lDayRangeNonLeap = 平年的2月日期只能是1-28
lDayRangeErr = 日期只能是1-31
lDayRangeErr1 = 日期只能是1-30
lHourNumErr = 时只能填入数字
lHourRangeErr = 时数字只能是0-23
lMinuteNumErr = 分只能填入数字
lMinuteRangeErr = 分数字只能是0-59
lSecondNumErr = 秒只能填入数字
lSecondRangeErr = 秒数字只能是0-59
lNTPServerIPErr = NTP服务器的IP地址只能有4组数字
lNTPServerIPNumErr = NTP服务器的地址如果设为IP地址格式的话只能是数字
lNTPServerIPRangeErr = NTP服务器的地址如果设为IP地址格式的数字要小于255
lNTPServer = 网络时间协议服务器
lTimeZone = 时区
lselTZGMTEast12 = GMT+12 奥克兰, 惠灵顿, 斐济, 马绍尔群岛
lselTZGMTEast11 = GMT+11 马加丹, 新喀里多尼亚, 所罗门群岛
lselTZGMTEast10 = GMT+10 堪培拉, 关岛, 墨尔本, 悉尼, 海参崴
lselTZGMTEast9 = GMT+9 大阪, 札幌, 首尔, 东京, 雅库茨克
lselTZGMTEast8 = GMT+8 北京, 重庆, 香港, 乌鲁木齐
lselTZGMTEast7 = GMT+7 曼谷, 河内, 雅加达
lselTZGMTEast6 = GMT+6 阿斯塔纳, 达卡, 叶卡捷琳堡
lselTZGMTEast5 = GMT+5 塔什干, 伊斯兰堡, 卡拉奇
lselTZGMTEast4 = GMT+4 莫斯科,耶烈万, 第比利斯, 圣彼得堡, 马斯喀特
lselTZGMTEast3 = GMT+3 内罗比, 科威特, 利雅得
lselTZGMTEast2 = GMT+2 开罗,  赫尔辛基, 加里宁格勒, 南非, 华沙
lselTZGMTEast1 = GMT+1 柏林, 布鲁塞尔, 哥本哈根, 马德里, 巴黎, 罗马
lselTZGMT = GMT 格林尼治
lselTZGMTWest1 = GMT-1 都柏林, 伦敦, 里斯本, 卡萨布兰卡
lselTZGMTWest2 = GMT-2 阿森松群岛, 圣赫勒拿岛
lselTZGMTWest3 = GMT-3 巴西利亚, 布宜诺斯艾利斯, 乔治敦, 福克兰群岛
lselTZGMTWest4 = GMT-4 大西洋时间(加拿大), 加拉加斯, 拉巴斯
lselTZGMTWest5 = GMT-5 东部时间(美国和加拿大), 波哥大, 利马, 基多
lselTZGMTWest6 = GMT-6 中部时间(美国和加拿大), 墨西哥城
lselTZGMTWest7 = GMT-7 山区时间(美国和加拿大), 亚利桑那
lselTZGMTWest8 = GMT-8 太平洋时间(美国和加拿大), 提华纳
lselTZGMTWest9 = GMT-9 阿拉斯加
lselTZGMTWest10 = GMT-10 夏威夷
lselTZGMTWest11 = GMT-11 中途岛, 萨摩亚群岛
lselTZGMTWest12 = GMT-12 埃尼威托克岛, 夸贾林环礁
lNtpStatus	= 网络时间协议状态
lNtpEnabled = 开启
lNtpDisabled = 关闭

 #DHCP Settings
lDhcpSettings = DHCP设置
lDhcpServer = DHCP服务器 :
lDhcpConfSet = DHCP 配置
lDhcpRange = DHCP范围 :
lDhcpStartAdd = DHCP起始地址 :
lDhcpEndAdd = DHCP结束地址 :
lDevLanIP = 设备局域网的IP地址 :
ldhcplt = DHCP持续时间 :
lDnsName1 = DNS1 IP 地址:
lDnsName2 = DNS2 IP 地址:
lDnsIpError = DNS IP 地址非法
lMinutes = (秒)
lIPErrorMsg = 输入有效IP地址
lErrorNumber = 您必须输入合法的 DHCP范围
btnAddStaticIP  = 增加静态IP
ltMacAddress = 物理地址
ltIPAddress = IP地址
lStaticIP_MAC = 物理地址:
lStaticIP_IP = IP地址:
h1AddStaticIP =  静态IP设置
btnAdd_dhcp = 增加
lDhcpAddrRangeError = DHCP起始地址大于结束地址
lDhcpStartAddrError = DHCP起始地址至少从192.168.X.2开始
MaxStaticIpError	= 最多支持增加30条静态IP
StaticIpAndRangeNotSameError	= 请确保DHCP Range和静态IP在相同的网段
DHCPV6title = DHCPV6设置
lDhcpV6Server = DHCPV6服务器:
lstatelessServer = 无状态自动配置
lstatefullServer = 有状态自动配置
lt_dhcp_stcRedirectionFunction = 重定向功能
lt_dhcp_stcRedirectionURLEnabled = 开启
lt_dhcp_stcRedirectionURLDisabled = 关闭
lt_dhcp_stRedirectionURL = 重定向URL
lStaticIpAddrError = 非法IP地址，有效的地址范围为 
DNSEnableSwitchLabel = DNS 配置开关
ClosedivDNSEnableSwitch = 关闭
OpendivDNSEnableSwitch = 开启

#Inter Connection settings
lICS = 因特网连接设置
lMICI = 因特网连接类型 :
lMIsp = ISP是 :
disptext2 = 文档位置
lCustomDNS = 自定义DNS
disptext3 = 覆盖默认的DNS设置
disptext4 = 我想克隆MAC地址
btGetMAC = 获得本机 MAC 地址
lIP_Address = IP地址 :
lMask = 子网掩码 :
lGateway_Address = 网关地址 :
lPrimDNSAdd = 主要的DNS地址 :
lSecondDNSAdd = 次要的DNS地址 :
lCustomeDNS1 = 自定义DNS 1 :
lCustomeDNS2 = 自定义DNS 2 :
lmaxLengthError64 = 密码最大字符不超过64位
lminLengthError8 = 密码最小不低于8个字符
lLengthError5 = 密码限定5个字符
lLengthError13 = 密码限定13个字符
lLengthError8_64 = 密码限定最少8个最多64 ascii 字符
lLengthError8_64hex =  密码限定最少8个最多64 ascii hex 字符
lProfile = 可选的Profile:
lPUsername = 用户名:
lPPassword = 密码:
lAccessNumber = 接入数字:
ltadvanceds =高级设置
h1AddNewProfile = 增加配置
h1EditProfile = 修改配置
lAuthType = 认证类型:
lConnMode = 连接模式:
lIdle = Idle Time:
lProfilename = 配置名称：
lAPN = 2G/3G APN名称:
lLteAPN = LTE APN名称:
lCUsername = 用户名:
lCPassword = 密码:
lCAccessNumber = 接入数字:
lCAuthType = 认证类型:
lCConnMode = 连接模式:
lCIdle = Idle Time:
lseconds = (秒)
ltProfilename = 配置名称
ltActive = Active
ltAPNname = APN 名字
ltConnMode = 连接模式
EMPTY_PROFILE_NAME = 配置名称不能为空（只能英文字符）
EMPTY_APN_NAME = APN 名字不能为空
NOT_VALID_IDLE = 空闲时间要大于零.
lWorkMode=工作网络模式
lVersionSwitch = 版本切换
lBootMode=优选工作模式
lBootMode1=优选工作模式
lBootMode2=优选工作模式
lBootMode3=优选工作模式
lBootMode4=优选工作模式
btnUpdateISP = 增加新的配置
dropdown_auto = 自动
dropdown_manual = 手动
lsetLikeLTEType = 优选的LTE类型
dropdownDisable = 禁用
dropdownEnable = 开启
dropdownMultimode = 4G/3G/2G多模
dropdown4Gonly = 仅4G
dropdown5Gonly  = 仅5G
dropdown45Gonly = 4G/5G
dropdown43Gonly = 4G/3G
dropdown32Gonly = 3G/2G
dropdown3Gonly = 仅3G
dropdown2Gonly = 仅2G
dropdownDisableNw = 自动
dropdown5GPre = 5G优先
dropdown4GPre = 4G优先
dropdown3GPre = 3G优先
dropdown2GPre = 2G优先
dropdown2GPreInBootMode2 = 2G优先
dropdown3GPreInBootMode2 = 3G优先
dropdownTDPre = TD-LTE优先
dropdownFDDPre = LTE FDD优先
dropdown14GPre = 4G优先
dropdown13GPre = 3G优先


lPDP_default = PDN1设置:
lPDP_dedicate = PDN2设置:
pPDPdef_lable = PDN1(默认承载)
sPDPdef_lable = 专用承载
pPDPded_lable = PDN2(默认承载)
s1PDPded_lable = 专用承载1
s2PDPded_lable = 专用承载2
s3PDPded_lable = 专用承载3
pPDPdef_chk = 设置
sPDPdef_chk = 设置
pPDPded_chk = 设置
s1PDPded_chk = 设置
s2PDPded_chk = 设置
s3PDPded_chk = 设置
sPDPtft_lable = TFT设置
s1PDPtft_lable = TFT设置
s2PDPtft_lable = TFT设置
s3PDPtft_lable = TFT设置
btnAddTFTRule  = 添加

h1Popup_PDP = PDP设置
lRuleename = PDP连接名称
lIPType = IP类型
dropdown_IPV4 =  IPV4
dropdown_IPV6 =  IPV6
dropdown_IPV4V6 = IPV4V6
lQOSQCI = QCI等级
lQOSEnbaletitle = QOS可用
lQciCheckError = QCI的值必须是大于0的整数值

l2G3GAuthType = 2G3G 鉴权类型
l2G3GUser	= 2G3G 用户名
l2G3GPassword = 2G3G 密码
l4GAuthType = LTE 鉴权类型
l4GUser	= LTE 用户名
l4GPassword = LTE 密码
APN_AHTU_USER_NAME_INVALIDEATE=用户名不支持中文字符


lTFTIPAddress = 远程IP地址
IPV4Radio = IPv4
IPV6Radio = IPv6
lTFTTabletitle = TFT表格
lLocalPort = 本地端口范围
lRemotePort = 远程端口范围
ltTFTname = TFT规则名称
ltIPType = IP类型
ltRemoteIP = 远程IP
ltLocalPort = 本地端口
ltRemotePort = 远程端口
lRuleName_TFT = TFT规则名称
btnAddNewTFTRule = 增加
h1TFTTableRule = TFT规则表格
h1AddTFTRule = 增加TFT规则
lpacketfilter_index = 数据包过滤器标识符
levaluation_index = 评价优先指数
ldirection = 方向
dropdown_uplink = 上行
dropdown_downlink = 下行
lIPAddress_TFT = IP地址
lSubnetmask_TFT = 子网掩码
lprotocol_TFT = 协议号
lIPV4Address_TFT = IP地址
lV4Subnetmask_TFT = 子网掩码
lemptyTFTname = 空TFT规则名称
lspecialTFTname = TFT规则的名字不应该有特殊的字符
lLocalPortInvalid = 本地端口无效,端口值的范围为1到65535
lRemotePortInvalid = 远程端口无效,端口值的范围为1到65535
lRuleNameIsChinese = 规则名称不支持中文字符
lIncorrectIPAddress = 无效的远程IP地址
lIncorrectNetMaskAddress = 无效的子网掩码地址
lDataPacketFilterIdError = 数据包过滤器标识符取值非法，取值范围为1到8之间的整数。
lAssessPrioritiesError = 评价优先指数取值非法，取值范围为0到255之间的整数。
lProtocolNumError = 协议号取值非法，取值范围为0到255之间的整数。
lsubsetMaskError = 子网掩码错误
lRoamingDisableAutoDialTip = 漫游状态下禁止自动拨号
lautoSwitch = 自动切换版本
lMTULabel = 最大传输单元（MTU）
lMtuInvalidTip = MTU值非法，取值范围为1000到1500之间的整数。
APN_NAME_INVALIDEATE = APN名字非法，不支持中文字符和特殊字符百分号(%)、逗号(,)、插入符(^)和分号(;)。
lAutoAPNLabel = 自动APN
lAutoConfigureAPNCheckBox = 自动适配APN

DialInRoamingLabel = 支持数据漫游
EnabledDialInRoaming = 支持
DisabledDialInRoaming = 禁止

EngineeringModelLabel = 工程模式
OpenEngineeringModel = 开启
CloseEngineeringModel = 关闭
queryTimeIntervalLabel = 查询时间间隔
queryTimeIntervalUnitLabel = 分钟

#Manual Network
h1ManualScanNetwork = 手动搜索网络
lManualNetworkStart = 开机手动选择网络
lMannualNetwork = 可选的网络:
dispmanualnetworktext = 设备启动时手动选择网络
lBgScanTime = LTE背景扫描间隔时间
btUpdate1 = 手动搜网
lManualPromte = &nbsp; &nbsp; 手动搜网可能需要几分钟，请耐心等待
dropdownImmediate = 立刻
dropdown30sec = 30 秒
dropdown1M = 1 分钟
dropdown3M = 3 分钟
dropdown5M = 5 分钟
dropdown10M = 10 分钟
dropdown15M = 15 分钟
dropdown30M = 30 分钟
dropdown60M = 60 分钟
dropdownLteTimeDisable = 禁用
dropdownAuto = 自动
completeScanNetwork	= 搜索网络结束，你可以进行选网操作
waitScanNetwork =  正在搜网，请等待
selectEmptyNetworkTypeErrorTip = 选择的网络类型不能为空
lScanNetworkError = 搜网失败，请重新搜网
lScanNetworkTimeOut = 搜网操作超时，请重新搜网
lSimCardAbsent		= 缺少SIM卡，请插入SIM卡继续搜索网络
lPinEnable		= SIM卡卡被锁定，请输入PIN码解开SIM卡
lPukEnable		= SIM卡的PIN码锁定，请输入PUK码解锁
CurrentScanModeLabel = 当前选网模式
AutoSelectNWMode = 自动模式
ManualSelectNWMode = 手动模式
UnkownSelectNWMode = 未知模式


#message
contentID=内容：
contactID=联系人：

textarea_msg = 您还可以输入字符数为 :
btnSend = 发送
onlySendOneSms = 只能输入一条短信内容
h1SendMessage = 写短信
sendSucess = 发送成功
sendFail = 发送失败
SendSMSbtn = 发送短信
btnSendOK = 确认
h1SendResult = 信息发送结果
#Message
lMessageStatsFrom = 发送者
lMessageStatsSubject = 内容
lMessageStatsReceived = 接收时间
lMessageStatsStatus = 状态
lMessageStatsLocation = 位置
lMessageReportTitle = 短信发送报告
lMessageReportSuccessReceive = 成功接收短信。
lMessageReportFailedReceive = 接收短信失败。

h1delsmsConfigure = 删除确认
btnConfirm = 确定
lUnread = 未读
lReaded = 已读
lUnsent = 未发送
lSent = 已发送
h1MessageContent = 短信
lDeleteMessage =  &nbsp; &nbsp;您确定删除所选短信吗?
lMessageFull = &nbsp; &nbsp; 邮箱已满，请删除短信!
btUpdate_Delete = 删除
btnOK_confirm = 确定
mMessage = 短信

#WiFi Hotspot
btnScanWirelessNw = 扫描无线网络
ltWirelessNws = 选择无线网络连接
lpsk = 网络Key:
lRetypepsk = 确认网络 Key:

#primary network
WifiFrequencyBand = WIFI频段:
lNetwork = 网络：
lNwStatus = 网络是 :
lSSID = 网络名字(SSID) :
lNwVisiStatus = 网络状态:
lWireSecurity = 无线安全:
lpass = 密码 :
lunmaskpass = 显示密码
lwpa = WPA密码 :
lAuth = 认证 :
lEncryption = 加密 :
lKeyType = 键值类型:
lRetypePassword =  重新输入密码 :
lInvalidPassword = 无效密码
lPasswdSpaceError = 密码不能包含空格
lAsciiPasswdLength5Error = 密码必须是ASCII字符并且密码长度为5个字符
lAsciiPasswdLength13Error = 密码必须是ASCII字符并且密码长度为13个字符
lHexPasswdLength10Error = 密码必须是十六进制字符(0-9,a-f,A-F)并且密码长度为10个字符
lHexPasswdLength26Error = 密码必须是十六进制字符(0-9,a-f,A-F)并且密码长度为26个字符
lWiFiProtSetup = 无线安全设置:
btnAddWPSClient = 添加WPS客户端
btnResetToWPSDefault = 重置WPS默认值
lexternalSorce =  设备可以从外部进行配置.
lRouterPin = 路由器PIN:
h1AddWPSClient = 添加WPS客户端
lWPStext = 选择联接WPS认证方式
lCancelWpsSession= 取消匹配
lWpsMatchPro = WPS进行匹配中...
lWpsMatchSuccess = WPS匹配成功
lWpsMatchFailed = WPS匹配失败
lWpsMatchInterrupt = WPS匹配被中断
lWpsPinCheckFail	= PIN码校验失败
spanWPSPushButton = WPS 配对按钮
lWpsCfgBtn = 添加WPS客户端
lNoneEncrypModeTip = 无线网络为非加密模式，是否继续？
lWpsDisabledInWepMode = WPS在WEP加密模式下不可用，是否继续？
lWpsDisabledInSsidHideStatus = WPS在网络隐藏状态下不可用，是否继续？
spanEnterPin = 输入 PIN
btnPush = 按键
btnRegister = 注册
btnClose = 关闭
lnoWPSforWAPI = 安全模式不支持WPS
lnoWEPfor11n = 802.11n 模式下,WEP加密不可用.

WPSStatus0 = 联接中断
WPSStatus1 = 联接成功
WPSStatus2 = PIN配对激活
WPSStatus3 = 联接未开始
WPSStatus4 = 配对中
WPSStatus5 = PIN 校验失败.
lWPSPinError = Pin须为8个或4个数字字符
lssidlenError = ssid最大长度为32

dropdownNone = 无加密
dropdownWPA2 = WPA2加密
dropdownWPAWPA2 = WPA-WPA2混合
dropdownWPA = WPA加密
dropdownWEP =  WEP加密
dropdownWAPI = WAPI加密
dropdown64b = 64位 - 5位ASCII/10位十六进制字符（0x开头）
dropdown128b = 128位 - 13位ASCII/26位十六进制字符（0x开头）
dropdown64bASCII = 64 比特 - 5位ASCII 字符
dropdown64bHEX = 64 比特 - 10位十六进制字符
dropdown128bASCII = 128 比特 - 13位ASCII字符
dropdown128bHEX = 128 比特 - 26位十六进制字符
dropdownShare = 共享
dropdownOpen = 开放
AES_Stronger = AES (强)
TKIP_Strong = TKIP (中)

#Dashboard

h2Internet = 因特网
h2Router = 路由器
h2HomeNw = 家庭网络

h3CellularConnection = 网络连接
h3WiFiConnection = WiFi 连接
h3IntrenetConnection = 因特网连接
h3IntConn = 通信量统计
h3RouterLANIP = 设备局域网的IP地址
pDashRouterMask = 设备局域网的子网掩码
pDashRouterMAC = 设备的MAC地址
pDashRouterRunTime = 设备的运行时间
pDashBetteryInfo = 设备的电池充电状态
pDashBetteryVol = 设备的电池电量
h3RouterInfo = 网关状态信息
pDashRouterLANIP = 设备局域网的IP地址
pDashRouterImeiTag=IMEI:

h3Firewall = 防火墙
h3UsbFileSharing = USB文档共享
h3DynamicDNS = 动态 DNS
h3SoftInfo = 软件信息
h3ConnDevice = 连接设备
h3Wireless = 无线
h3WirelessS = 无线设置
h3WPS = WiFi 保护设置
h3DhcpServer = DHCP服务器 :

pCellular = 网络数据连接:
pCellularMode = 网络模式:
pSignalStrength = 信号强度:
pRoaming = 漫游:
pSIMStatus = SIM 状态:
pPINStatus = PIN/PUK 状态:
pWanStatus = WAN连接状态 :
pWanConnStatus = 连接状态:
pConnType = 连接类型 :
pDashIPAddress = IP地址 :
pDashConnTime = 连接时间:
pDashDefaultGateway = 默认网关 :
pDashNetworkMask = 子网掩码 :
pdashDNS = DNS服务器 :
pSentPackets = 发送
pRecPackets = 接收
pDeviceModel = 设备类型 :
pSoftVersion = 软件版本
pHardVersion = 硬件版本

SPdpInformation = PDP 信息
Sconnum = PDP 连接数
SpdpType = PDP 类型
SpdpSuccess = PDP 连接状态
SIPV4Address = IPv4地址
SIPV4DNS = IPv4 DNS服务器
SIPV4Gateway = IPv4默认网关
SIPV4Netmask = IPv4子网掩码
SIPV6Address = IPv6地址
SGIPV6Address = 全局IPv6地址
SIPV6DNS = IPv6 DNS服务器
SIPV6Gateway = IPv6默认网关
SIPV6Netmask = IPv6子网掩码
pDashCurConnTime = 本次连接时间
pDashTotalConnTime = 总连接时间
SPDPType_Default = 默认
SPDPType_Custom = 定制
# pUpdatesAvailable =  可更新
pWirelessNw = 无线网络名字
pWiFiWirelessNw = 无线网络名字
pSecurityMode = 安全模式 :
pChannelNumber = 信道数 :
pWirelessPNw = 主无线网络 :
pWirelessGNw = 其他无线网络 :
lrestartWB = 数据连接已建立，请重启浏览器。
lPValue = (安全性:
lGValue = (安全性:
lSIMPresent = 有SIM
lSIMAbsent = 无SIM
lSIMLocked = 锁定
lSIMerror = 未知错误
resetTraffic = 清零
lUncharged = 未充电
lCharging = 充电中
lFullycharged = 已充满
lNoBattery = 无电池
lconnected = 已连接
ldisconnected = 未连接
lconnecting   = 正在连接
ldDays = 天
ldDay = 天
ldHours = 小时
ldHour = 小时
ldMinutes = 分
ldMinute = 分
ldSeconds = 秒
ldSecond = 秒

h2AutoApn = 自动适配APN信息
pDashAutoApn_mmcTag = MCC:
pDashAutoApn_mncTag = MNC:
pDashAutoApn_OperatorNameTag = 运营商名称:
pDashAutoApn_ApnTag = APN:
pDashAutoApn_LteApnTag = LTE APN:
pDashAutoApn_NetworkTypeTag = 网络类型:
pDashAutoApn_authtype2g3gTag = 2G3G 鉴权类型:
pDashAutoApn_username2g3gTag = 2G3G 用户名:
pDashAutoApn_password2g3gTag = 2G3G 密码:
pDashAutoApn_authtype4gTag = 4G 鉴权类型:
pDashAutoApn_username4gTag = 4G 用户名:
pDashAutoApn_password4gTag = 4G 密码:
pDashAutoApn_iptypeTag = IP 类型:

#Connected Device
ltName = 名字
ltIpAddress = IP地址
ltDeviceStatus=状态
ltMac = 物理地址
ltBlocked = 阻塞
btnModalOk = 确认
btnModalReset = 移出设备登陆
lModalHeader = 设备名稱：
h1DeviceHeader =  编辑设备
ltStatus = 访问以太网
lBlock = 阻塞
ltTime =  连接时间
ltStatus1 = 状态
State = 禁用
#Disable device
Disable_Devices = 已禁用设备
DisableMac = MAC地址
DisableName = 已禁用设备名
Recover = 恢复

#Firewall Settings
lFirewallSettings = 防火墙设置
lVPN = 虚拟专用网 (VPN)
lFirewallProtection = 防火墙保护 :
lIPSecPassthrough = IPSec可通过 :
lPPTPPassthrough = PPTP可通过 :
lL2TPPassthrough = L2TP可通过 :

#Network Activity
lLogintime = 上次登录时间：
ltDialLogDeleteBtn = 删除
ltClientLogDeleteBtn = 删除

#DDND Settings
lDDNS = 动态域名系统(DDNS)
lDDNSService = DDNS服务 :
lDDNSUsername = 用户名 :
lDDNSPassword = 密码 :
lDDNSHostname = 主机名 :
lDDNSType = 类型 :
lDDNSWildcard = 通配符 :
lDDNSErrUsernameEmpty = 要求的用户名.
lDDNSErrPasswordEmpty = 要求的密码.
lDDNSErrHostnameEmpty = 要求的主机名.

#Storage Settings
lStorageSettings = USB设备文档共享 :
lDeviceUsage = 存储设备使用
ltSDName = 名字
ltSDUsedSpace = 已用空间
ltSDAvailableSpace = 可用空间
btnSafelyRemoveDevices = 安全移出设备

#Diagnostics
lSelTool =  选择工具
lEnterParams =   输入参数
lOutput = Output
lSelectToolErrorMsg = 请先选择工具，然后点击运行。
lInvalidCharErrorMsg = 输入非法字符，请重新输入。

#Layout Manager
tInternet = 因特网
tHome_Network = 家庭网络
tWireless = 无线
tDashboard = 操作面板
tRouter =  路由器
tStorage = 存储

mTrafficStats = 通信量统计
mNWActivity = 用户列表
mInternetConn = 因特网连接
mManulNetwork = 手动选网
mWiFiConn = WiFi 热点
mDDNS =  动态域名服务
mReboot = 重启路由器
mTelnet = 远程登录
mFile_Sharing = 文件共享
mTheme = 更换主题
mMessage = 短信
mTimeSetting = 时间设置
mWebDav = SD卡文件
mWebDavmanagement = SD卡管理
mAcsManagement = ACS管理

mPinPuk = PIN码管理
lAttempts = PIN/PUK尝试次数
lPINAttempts = PIN 尝试次数
lPUKAttempts = PUK 尝试次数
lProvidePin = 输入PIN
lResetPin = 重置PIN
lEnablePin = 使能PIN
lDisablePin = 禁用PIN
lChangePin = 修改PIN
lEnterPuk = 输入PUK:
lEnterPin = 输入当前PIN:
lEnterPin1 = 输入当前PIN:
lEnterNewPin = 输入新PIN:
lEnterNewPin1 = 输入新PIN:
lUnknownNoSIM = 没有SIM卡或者未知的错误
lPukExhausted = SIM卡永久锁定
lPinExhausted = 解PIN的次数用完， 请提供PUK重置PIN.
linvalidPin = 无效的PIN. PIN 的长度大于4且小于6.
lNewPinSameWithOld = 新PIN码和旧PIN码相同
linvalidPuk = 无效的PUK. PUK 的长度大于4且小于10.
lpinattempts = 可以继续输入PIN的次数:
lpukattempts = 可以继续输入PUK的次数:
btUpdate0 = 保存
btUpdate2 = 保存
lPINrequired = 需要
lMEPUnlockrequired = 请先在MEP设置中进行解锁MEP之后再解PIN
lPinPasswordError =  PIN码密码错误
lSimNotSupportPinCode = SIM卡不支持该PIN码
lSimPUKRequest = 需要输入SIM卡PUK码
lFailedWithUnkown = 未知原因错误

mMEPSetting = MEP设置
lUnlockPINBtn = PIN解锁
lEnableLockPINBtn = 开启PIN锁
lMEPPNpasswordSetting =	PN设置
lMEPPNpassword = PN密码
lUnlockMEPPNBtn = PN解锁
lEnableLockMEPPNBtn = 开启PN锁
lInvalidLockMEPPNBtn = PN锁不可用 
lMEPPNpukBtn = PN PUK 解锁
lMEPPNpukSetting = PN PUK 设置
lMEPPNpuk = PN PUK
lMEPPUpasswordSetting =	PU设置
lMEPPUpassword = PU密码
lUnlockMEPPUBtn = PU解锁
lEnableLockMEPPUBtn  = 开启PU锁
lInvalidLockMEPPUBtn = PU锁不可用 
lMEPPUpukSetting = PU PUK设置
lMEPPUpuk = PU PUK
lMEPPUpukBtn = PU PUK解锁
lMEPSPpasswordSetting = SP设置
lMEPSPpassword = SP密码
lUnlockMEPSPBtn = SP解锁
lEnablelockMEPSPBtn = 开启SP锁
lInvalidLockMEPSPBtn = SP锁不可用 
lMEPSPpukSetting = SP PUK设置
lMEPSPpuk = SP PUK
lMEPSPpukBtn = SP PUK解锁
lMEPPCpasswordSetting = PC设置
lMEPPCPpassword = PC密码
lUnlockMEPPCBtn = PC解锁
lEnableLockMEPPCBtn = 开启PC锁
lInvalidLockMEPPCBtn = PC锁不可用 
lMEPSIMpasswordSetting = SIM设置
lMEPSIMpassword = SIM密码
lUnlockMEPSIMBtn = SIM解锁
lEnablelockMEPSIMBtn = 开启SIM锁
lInvalidLockMEPSIMBtn = SIM锁不可用
lMEPPNLeftRetry = MEP PN 剩余重试次数：
lMEPPUAttempts = MEP PU 剩余重试次数：
lMEPSPAttempts = MEP SP 剩余重试次数：
lMEPPCAttempts = MEP PC 剩余重试次数：
lMEPSIMAttempts = MEP SIM 重试次数
lMepSimPukSetting = SIM PUK 设置
lMepSimPuk = SIM PUK 
lMepSimPukAlertError = SIM PUK 错误 
lMepSimPukBtn = SIM PUK解锁
lMepPcPukSetting = PC PUK 设置
lMepPcPuk = PC PUK
lMepPcPukAlertError = PC PUK 错误
lMepPcPukBtn = PC PUK解锁
lPasswordError = 密码错误

mConnected_Devices = 已连接设备
mAccess_Logs = 日志
mDHCP_Settings = DHCP 设置
mFirewall_Settings = 防火墙设置
mAccess_Control	= 访问控制
mApp_Gaming = 应用程序与游戏
mPort_Forward = 端口映射 


mWire_Set = 无线设置
mWire_Sec = 无线安全设置
mPNSS = 主要网络安全设置
mONSS = 其他网络安全设置
mWMAC = 无线MAC过滤

mStorageSettings = 存储设置

mUserManage = 用户管理
mConfManage = 配置管理
mSoftUpdate = 软件升级
mDigo = 诊断
mTimeZone = 时区

mDashboard = 面板接口区

#Software Upgrade
lSoftwareInfo = 软件信息
lCurrentSoftVersion = 目前软件版本：
lCurrentSoftwareDate = 目前软件日期：
lSoftwareInfo = 软件信息
lSoftwareInfoText = 如果您有更新软件，你可选择手动更新！
lCheckNewSoft = 确认新软件
lCheckNewSoftText = (您可确认是否有新软件可供更新)
lAutomatic_Upgrade = 自动升级
btUpgrade = 升级
btCheck = 验证
btSetAsADefault = 设置为默认
h1RouterUpgrade = 由器升级
lUploading = 装载中
lUpgrade = 升级中
lDownload = 固件下载中
lBurnFirm= 固件烧写中
lReboot = 重启。请稍候。。。
lSuccessReboot = 固件升级成功， 重启...
lFailReboot = 固件升级失败,  重启...
lUpgradeError =  升级失败：
lWarning = 警告！
lReminder = 提醒
pISPName = 网络运营商:
pRoamingNetworkOperator = 路由网络运营商:
lSoftwareWarningText = 警告!!!请使用USB连接进行升级！
lWarningLine1 = 升级固件需要花一些时间。
lWarningLine2 = 不要按下电源或开关键！
ErrorEnterName = 输入命名规则
lsoftwareError = 请选择需要更新的文件
btGetSoftVersion = 浏览
lUpgradeInfoText = 从SD卡中获取固件升级包.
h1SDUpgradePopup =  SD升级
lSDUpgradeSuccessText = 从SD卡升级固件成功
lSDUpgradeFailedText = 从SD卡升级固件失败

#Time Zone
ldeviceTimeZone = 所在时区：


#ERROR
NET_ERR_STATUS_OK               =       OK
NET_ERR_SWUP_FILE_SIZE          =       文件太大
NET_ERR_SWUP_BAD_URL            =       不是 http 或 ftp url
NET_ERR_SWUP_CONNECTION         =       服务器停滞或没有响应
NET_ERR_SWUP_DOWNGRADE          =       下调
NET_ERR_SWUP_REMOTE_NEEDED      =       需要远程更新
NET_ERR_SWUP_BAD_FILE           =       图片文件损坏
NET_ERR_SWUP_BURN_FAILED        =       烧写操作失败
NET_GENERIC_ERR                 =       未知错误
NET_ERR_REMOTE_NON_SUPPORT      =       远端不支持
UNKNOWN_ERROR                   =       未知错误

#Application & Gaming
ltRuleName = 规则名称
ltAppName = 应用程序名称
ltExternalPorts = 外部端口
ltInternalPorts = 内部端口
ltLocalIPAddress = 本地IP地址
ltProtocol = 协议
ltTRuleName = 规则名称
ltTAppName = 应用程序名称
ltTExternalPorts = 外部端口
ltTInternalPorts = 内部端口
ltTLocalIPAddress = 本地IP地址
ltTProtocol = 协议
lDescription = 可以输入您的客制化规则.
lPortForwordingText = 打开了多个端口，数据通过这些端口到局域网内的一个设备。推荐使用静态IP来映射端口。
lPortTriggeringText = 当设备检测到有数据发送到网络的触发端口打开单个或多个端口。这个特殊的规则适用局域网内的所有计算机。
h1PortFwdRule = 端口映射规则
lAppName = 应用程序名称 :
lRuleName_fw = 规则名称 :
lExtPortRange = 外部端口范围 :
lLocalPortRange = 本地端口范围 :
lLocalIPAddress = 本地IP地址 :
lProtocol = 协议 :
h1PortTrgRule = 端口触发规则
lAppNameTrigger = 应用程序名称 :
lRulenameTrigger = 规则名称 :
lExtPortRangeTrigger = 外部端口范围 :
lLocalPortRangeTrigger = 本地端口范围 :
lProtocolTrigger = 协议 :
lAppAndGamingErrorFwd = IP地址是无效的
lAppAndGamingErrorTrg = IP地址是无效的
lStandardPortError = 标准端口触发不能被编辑
lAppAndGamingErrorFwdNum = 端口号须为数字
lPortForwording = 端口转发
h1PortFwdRule = 端口转发规则
h1PortTrgRule = 端口触发规则
MaxRuleError = 最多支持增加8条规则

mCustom_FW = 用户防火墙规则
h1CustomFWRule = 用户防火墙规则
lCustomFWRulesText = 通过设定源IP地址（端口），目的IP地址（端口）或者这些参数的任意组合来控制设备访问网络。
ltSrcPort = 源端口
ltDstPort = 目的端口
ltSrcIP = 源IP地址
ltDstIP = 目的IP地址
ltEnabled = 启用
lSrcPort = 源端口
lDstPort = 目的端口
lSrcIP = 源IP地址
lSrcIP6 = 源IPV6地址
lDstIP = 目的IP地址
lDstIP6 = 目的IPV6地址
lStatus = Status
lIPType = IP类型 
btnAddRule = 增加规则
lCustomFWSpecialCharsNotAllowed= 名字中不允许出现 # , : , ' '(space), & , ; , ~ , | , < , > , $ 等特殊字符。
lAtleastOne = 请提供至少一个的源IP地址、源端口、目的IP地址和目的端口。
lIncorrectSrcIP = 源端 IP 地址不正确
lIncorrectDstIP = 目的端 IP 地址不正确
lIncorrectPort = 无效端口
lToFrmPort = 无效端口范围
lStatus_fw = 状态：
homeIPError = 请设定局域网的IP地址范围
lUplugAndPlay = 通用即插即用：
lPortFwdTrigSpecialCharsNotAllowed = 不可以有特殊字符如# , : , ' '(空格), & , ; , ~ , | , < , > , $.
lIPFilterMode = IP过滤模式
lDisabledFirewall = 端口过滤已开启，自定义防火墙规则不可用
IP6SpacesNotAllowed = IPV6地址中不允许出现空格

#Domain Name Filter
mDomain_Name_Filter = 域名过滤
lDNFilterRulesText = 通过设定域名，起始IP地址，终止IP地址或者这些参数的任意组合来控制设备访问网络。
lt_DNFilter_btnAddRule = 增加规则
lt_DNFilter_RuleName = 规则名称
lt_DNFilter_Enabled = 启用
lt_DNFilter_DomainName = 域名
lt_DNFilter_SrcIpStart = 起始IP地址
lt_DNFilter_SrcIpEnd = 终止IP地址
h1DNFilterRule = 域名过滤规则
lRuleName_DNF = 规则名称
lDomainName = 域名
lSrcIPStatrt = 起始IP地址
lSrcIPEnd = 终止IP地址
lIncorrectStartIP = 起始 IP 地址不正确
lIncorrectEndIP = 终止 IP 地址不正确
lDNF = 域名过滤：
DNSDenyList = 无效域名列表
DNSAllowList = 有效域名列表
lDNSDenyListEmpty = 不过滤任何域名，自动关闭域名过滤功能
MaxDNSRuleError = 最多支持增加10条域名过滤规则

#Port Forwarding
lCustomPWRulesText = 端口转发规则
h1CustomPWRule = 端口转发规则
lRuleName_pw = 规则名称
lIP_pw = 内网IP地址
lPort_pw = 端口范围
lProtocol_pw = 协议
ltPWRuleName = 规则名称
ltPWIP = 内网IP地址
ltPWPort = 端口范围
ltPWProtocol = 协议
lIncorrectPWIP = 无效的IP地址
lPWAtleastOne = 请至少提供IP地址或者端口范围

#Traffic Statistics
lTrafficStats = 通信量统计
lTrafficStatsInfo =网关通讯封包数据统计
lTrafficStatsWAN = 因特网连接 (WAN)
lTrafficStatsLAN = 家庭网络 (LAN)
lTrafficStatsUSB = USB 连接
lTrafficStatsWANSent = 发送 :
lTrafficStatsWANReceived = 接收 :
lTrafficStatsWANErrors = 错误 :
lTrafficStatsWLAN = 无线网 (WLAN)
lTrafficStatsWLANSent = 发送 :
lTrafficStatsWLANReceived = 接收 :
lTrafficStatsWLANErrors = 错误 :

#Theme Change
lChangeTheme = 主题选则

#Access Logs:
lAccessLogs =  访问网络日志
lClientAccessLogs =  无线终端连接设备日志
btnAddAccessLogsRule = 添加规则
ltDeviceName = 设备名称
ltDestination = 目的地址
ltService = 服务
ltFrequency = 频率
ltMostRecestAccess = 最近访问
lAccessControl = 访问控制
btnAccessControlRule = 添加
ltACRuleName = 规则名称 :
ltACDeviceMac =  设备物理地址 :
ltCid		= CID
ltIPType    = IP类型
ltIPv4Addr  = IPv4地址
ltIPv6Addr  = IPv6地址
ltPdpName = PDP名称
ltACStartTime = 开始时间
ltACEndTime = 结束时间
ltIPAddr = IP地址
ltACMACAddr = 客户端MAC地址
ltACConTime = 连接时间
ltDisconTime = 断开时间
h1InternetAccessRule = 因特网访问规则
h1InternetAccessRule1 = 添加因特网访问规则
lFromDevice = 源设备 :
lDeviceMac = 设备物理地址 :
lAllowAccessOn = 允许访问 :
Sun = 星期日
Mon = 星期一
Tue = 星期二
Wed = 星期三
Thu = 星期四
Fri = 星期五
Sat = 星期六

ltConnection = 连接

lLogStartTime = 开始时间：
lLogEndTime = 结束时间：
EMPTY_RULE_NAME = 不允许空命名规则
SPECIAL_CHARS_ARE_NOT_ALLOWED = 不可以有特殊字符如# , : , ' '(空格), & , ; , ~ , | , < , > , $.
MAC_IS_NOT_VALID = MAC地址无效
IP_IS_NOT_VALID = IP地址无效
INVALID_START_TIME = 无效的开始时间
INVALID_END_TIME = 无效的结束时间
START_TIME_LESS_ERROR = 开始时间应该小于结束时间
lLogRulename = 应用程序名称 :

# Begin Add by ycc, modify locale
# lproductName = Marvell无线路由器
lproductName = 无线路由器
# End Add by ycc, modify locale


#Quick Setup
h1UserSettings = 用户设置
h1InternetConnectionQS = 因特网连接
h1InternetConnection = 因特网连接
h1WirelessSeetings = 无线设置
h1DevicePlaceGuid = 无线设备放置建议
quickSetupText1 = 快速设置
lUserSettings1 = 用户设置
h1UserSettingsHeader = （推荐改成默认的用户名和密码）
lInterConnQS = 设备连接因特网的方式选择
lPrimaryQS = 无线网卡设置
lDevicePlaceGuidText = 最好不要把无线路由器放置在微波设备，无绳电话，婴儿看护器，蓝牙设备（如蓝牙耳机，蓝牙键盘或蓝牙鼠标）
                       及其它无线网络设备的附近。
btnFinish = 完成
# Begin Add by ycc, modify locale
# QsText = 欢迎！谢谢选择Marvell
QsText = 欢迎！谢谢选择我们的产品
# End Add by ycc, modify locale
QsText1 = <B> 最好用快速设置来设置路由器。<B>
QsText2 = 请确认你是根据快速设置指南里的步骤连接线缆的。根据这个程序一步一步的配置好网络连接。
QsText3 = 不要显示快速设置。
QsText4 = 备注：即使跳过还是会在界面上看到快速设置链接。
btnSkip = 跳过
btnQuickSetup = 快速设置

# Config Management
lConfOption = 配置选项
lConfOptionText = 保存设置到文件。
lConfFile = 选择一个配置文件
lConfFileText = （选一个配置文件来恢复设置）
lResotreFactSetting = 恢复工厂设置
lResotreFactSettingText =  这个选项会恢复工厂设置。现有设置会被覆盖。
btnRestoreFactorySettings = 恢复工厂设置
btRestoreConf = 恢复设置
btnSave =  保存
h1Rebooting = 重启
lRebootingText = 网关将重启。
lRebootingText1 = 设置需重启后才能生效。
# btnModalOk = 完成
lConnect = Connect to wireless network
btnConnectW = Connect Anyway
btnConnectWP = Connect
h1Confirm = 确认
lConfirmText = 当前设置将丢失。继续吗？
btnModalCancle = 取消
SelectConfFileError = 请选择一个配置文件


lConfirmText1 = 确认要覆盖当前设置吗？
lConfirmTextReboot = 您确定重启路由器吗？
#lConfirmText1 = Are you sure you want to
lConfirmText7 = 恢复默认的 WPS 安全设置?
lConfirmText2 = 主题已修改。
lConfirmText3 = 直接到登录页面。
lConfirmText4 = 清除浏览器缓冲使主题生效。

lImportCfgFileText1 = 请选择bin格式的配置文件更新设备
lImportCfgFileText2 = 在更新前，请确保设备和PC进行USB连接
lImportCfgFileText3 = 更新配置文件后，设备将自动重启
btnBrowserFile      = 浏览
lFileFormatError    = 文件格式错误，请选择后缀名为bin的文件
btnUpdate           = 更新
lEmportCfgFileText	= 点击下面链接导出配置文件
lExportLink			= 导出配置文件
lExportCfgSuccess = 成功导出配置文件

#Reboot Router
lRebootRouter = 选择下面的按钮重启路由器
btRebootRouter = 重启路由器
h1RebootRouter = 重启路由器
lRebootedRouter = 重启，请稍候。。。
lQueryRebootedRouter = 确定要重启路由器？
btnRebootOK = 确定
btnCancle = 取消
lSaveAcatDumplogIntoSDText = 此选项要求用户选择是否将ACAT Dump文件保存到SD卡中，ACAT Dump文件用于调试
lSaveAcatDumplogSetting = ACAT Dump文件保存设置
lbtnSaveAcatDumpLogSetting = 保存
lSdFormatSupportStatus = SD卡格式化
lSdSupportFormat0 = 支持格式化
lSdSupportFormat1 = 不支持格式化
lSdSupportFormat2 = 不支持，其他原因
lSdSupportFormat255 = 没有SD卡

AllowModeError = MAC地址过滤模式为允许访问的MAC地址。添加到有效的MAC地址列表
DenyModeError = MAC地址过滤模式为不允许访问的MAC地址。添加到无效的MAC地址列表
NetworkDisabledError = 网络使能才能使MAC地址过滤的设定有效。

#wireless Mac filter
ltAllowDeny = 机器MAC地址
lDenyList = 无效MAC地址列表
thDeny = 机器MAC地址
thAllow = 机器MAC地址
lAllowList = 有效MAC地址列表
lMS = 模式设置：
h1AddMACFilter = 添加MAC地址过滤
lMACAddress =  MAC地址
btnAddMACFilter = 增加
lMF = MAC过滤：
lMacFilterItemError = 最多支持16条MAC地址
lDeleteListMessage = 确认删除勾选项？
h1dellistConfigure = 删除确认
lMacEnableListEmpty = 不允许任何客户端接入无线网络，自动关闭MAC地址过滤功能
lAllowMacFilterPrompt = 本客户端MAC地址不在列表中，可能导致无法连接无线网络，是否继续？

errorSelectConfFile = 请选择一个配置文件
Microwave = 微波
Bluetooth_Devices =蓝牙设备
Cordless_Phone = 无绳电话
ownDevices = 其它无线网络设备
Baby_Monitor = 儿童看护器

#SMS
tSms = 短信息
mDeviceInbox = 收信箱
mDeviceOutbox	= 发信箱
mSimSms	= SIM卡侧信息
mDrafts		= 草稿箱
mSmsSet		= 短信设置

lsmsReceiver = 收信人

lt_SmsSet_stcTitle	= 信息设置
lt_SmsSet_stcDeliveryReport	= 发送报告
lt_SmsSet_stcSaveLoc = 短信保存位置
lt_SmsSet_stcSaveLocSimCard	= SIM卡
lt_SmsSet_stcSaveLocDevice		= 设备
lt_SmsSet_stcSendReportEnabled	= 开启
lt_SmsSet_stcSendReportDisabled	= 禁止
lt_SmsSet_stcValidity				= 有效期
lt_SmsSet_stcTwelveHours			= 12小时
lt_SmsSet_stcOneDay					= 一天
lt_SmsSet_stcOneWeek				= 一周
lt_SmsSet_stcLargest				= 最长
lt_SmsSet_stcCenterNumber			= 中心号码
lt_SmsSet_btnSave					= 保存
lt_SmsSet_chkSavetoSim =  默认将信息保存至SIM卡  
lt_SmsSet_stcSMSOverCSMode =   短信发送模式
lt_SmsSet_stcPackageDomain  =  PS
lt_SmsSet_stcPackageDomainPrefer = PS优先
lt_SmsSet_stcCircuitSwitched = CS
lt_SmsSet_stcCircuitSwitchedPrefer = CS优先

lt_sms_stcTitle	= 收信箱		
lt_sms_stcFrom = 发送者
lt_sms_stcSubject = 内容
lt_sms_stcRecvTime = 接收时间
lt_sms_stcStatus = 状态
lt_sms_stcLocation = 位置
lt_sms_btnDelete	=删除
lt_sms_btnNew		= 新建
lt_sms_btnRefresh   = 刷新
lt_sms_btnCopy      = 复制
lt_sms_btnMove      = 移动
lt_sms_btnSaveDraft = 保存
lt_sms_btnSend = 发送
lt_sms_btnCancel = 取消
lt_sms_chooseNumberTip        = 最多可以选择5个联系人。
lt_sms_forwardSmsTip        = 转发信息
lt_sms_deleteSmsTip        = 删除信息
lt_sms_newSmsTip        = 新建信息
lt_sms_unreadSms		= 未读信息
lt_sms_readedSms		= 信息已读
lt_sms_sendSuccess		= 发送成功
lt_sms_sendFailed		= 发送失败
lt_sms_drafts			= 草稿短信
lt_sms_stcmeudelete     = 删除
lt_sms_stcmeucopy       = 复制
lt_sms_stcmeucopytosim     = 复制到SIM卡
lt_sms_stcmeucopytolocal   = 复制到设备
lt_sms_stcmeumove         = 移动
lt_sms_stcmeumovetosim     = 移动到SIM卡
lt_sms_stcmeumovetolocal   = 移动到设备
lt_sms_stcmeusavenumber    = 保存号码
lt_sms_stcmeuclearall      = 全部清空
lt_sms_stcmeurestore       = 还原
lsmsSimCardAbsent          = 缺少SIM卡，请插入SIM卡后重新操作
lsmsNoWirelessNetwork      = 没有无线网络，请连接无线网络之后重新操作
lsmsWarning					= 提示
lDeviceInboxCapacityFull			= 设备收件箱容量已满，请删除
lDeviceOutboxCapacityFull			= 设备发件箱容量已满，请删除
lDeviceDraftboxCapacityFull			= 设备草稿箱容量已满，请删除
lSimCardCapacityFull		= SIM卡容量已满，请删除
lMessageReportTitle = 短信发送报告
lMessageReportSuccessReceive = 成功接收短信。
lMessageReportFailedReceive = 接收短信失败。
lSMSCenterModificationWarning = 修改短信息中心号码可能会导致短信发送失败
lsmsNotification					= 提醒
lsmsNewArrivedSMS					= 新短消息
lsmsOneNewArrivedSMS					= 新短消息
lsmsMoreNewArrivedSMS					= 新短消息
lSendMessageFailed = 发送短信失败
lSaveMessageFailed = 保存短信失败
lDeleteMessageFailed = 删除短信失败
lMoveMessageFailed = 移动短信失败
lCopyMessageFailed = 拷贝短信失败
lCopyMessagePartialFailed = 拷贝短信部分失败
lMoveMessagePartialFailed = 移动短信部分失败
lOperateMessageReportTitle = 短信操作
lt_sms_stcSmsLenghtError = 最多发送640个英文或280个中文字符
lContactIsEmpty = 电话号码不能为空
lSmsIsEmpty = 短信内容为空
lSaveSmsError	= 只能保存一条短信到草稿箱
lPhoneNumberFormatError = 电话号码格式错误

#Port Filter page
mPortFilter								= 端口过滤
lt_portFilter_stcTitle					= 端口过滤
lt_portFilter_stcHelpContent			= 通过设置触发端口、相应端口或者这些参数的任意组合来控制设备访问网络。
lt_portFilter_stcMode					= 端口过滤模式
lt_portFilter_stcEnabledPortFilter		= 开启
lt_portFilter_stcDisabledPortFilter		= 关闭
lt_portFilter_stcSave					= 保存
lt_portFilter_btnAddPortFilterRules		= 添加端口过滤
lt_portFilter_stcRuleName				= 规则名称
lt_portFilter_stcProtocol				= 协议
lt_portFilter_stcTriggerPort			= 触发端口
lt_portFilter_stcResponsePort			= 响应端口
lt_portFilter_stcPortFilterDlgTitle		= 端口过滤对话框
lt_portFilter_btnCancelAddRule			= 取消
lt_portFilter_btnAddRule				= 添加
lStartPortIsEmpty						= 端口号不能全为空
lStartPortLarger						= 开始端口号不能小于结束端口号
lPortNumInvalide						= 端口号必须是1到65535之间的整数
lTriggerPortIncomplete					= 触发端口号配置不完整
lResponsePortIncomplete					= 响应端口号配置不完整
#device traffic data
mDataTraffic							= 设备流量
ltitle							= 设备流量
lClientName								= 名称
lDeviceName								= 名称
lNameType								= 名称类型
lNameTypeAssigned						= 指定
lNameTypeNotAssigned					= 未指定
deviceStatusSel							= 状态
DisconnectedStatus						= 断开
ConnectedStatus							= 连接
BlockStatus								= 阻塞
lConnType								= 连接类型
lIpAddr									= IP地址
lMacAddress								= MAC地址
lMacAddr								= MAC地址
lConnTime								= 总连接时间
lTotalTraffic							= 总流量
lStatus									= 状态
lDeviceInfoBoxTitle						= 设备信息
lLastConTime							= 上次接入时间
lTotalConTime							= 总连接时间
lMonthSendData							= 本月发送数据量
lMonthRecvData							= 本月接收数据量
lMonthTotalData							= 本月总流量
lLast3DaySendData						= 近三天发送数据量
lLast3DayRecvData						= 近三天接收数据量
lLast3DayTotalData						= 近三天总流量
lTotalSendData							= 总的发送数据量
lTotalRecvData							= 总的接收数据量
lTotalData								= 总数据流量
lDeviceStatus							= 设备状态
lBtnOk								= 确定
lAction									= 操作
lMaxBlockStatusTip						= 最多阻塞8个客户端
lBlock									= 阻塞
lUnBlock								= 开启
lStop									= 停止
lConnection								= 连接
lDisConnection							= 断开	
lBlocked								= 阻塞
lUnkownStatus							= 未知		
lBtnResetDevices						= 重置数据流量	

#mTrafficSetting
mTrafficSetting					= 流量设置
lt_trafficSet_stcTitle			= 流量控制设置
lt_trafficSet_stcStaticMode		= 流量控制模式
lt_trafficSet_stcDisabled		= 禁止
lt_trafficSet_stcMonth			= 按月设置
lt_trafficSet_stcPeriod			= 按周期设置
lt_trafficSet_stcMonthUsedTaffic			= 月已用流量
lt_trafficSet_btnCalMonthTraffic			= 流量校准
lt_trafficSet_stcMonthTotalTaffic			= 月流量限制
lt_trafficSet_btnChangeMonthTotalTraffic	= 修改
lt_trafficSet_stcPeriodUsedTaffic			= 周期已用流量
lt_trafficSet_btnCalPeroidTraffic			= 校准
lt_trafficSet_stcPeriodTotalTaffic			= 周期限制流量
lt_trafficSet_btnChangePeroidTotalTraffic	= 修改
lt_trafficSet_stcPeriodTime					= 周期起止时间
lt_trafficSet_stcPeriodStartTime			= 周期起始时间
lt_trafficSet_stcPeriodEndTime				= 周期终止时间
lt_trafficSet_btnChangePeroidTime			= 修改
lt_trafficSet_stcPeriodTimeSetting			= 周期时间设置
lt_trafficSet_stcNewPeriodTime				= 起止时间
lt_trafficSet_btnNewPeroidTime				= 保存
lt_trafficSet_btnNewTraffic					= 保存
lt_trafficSet_stcCancel						= 取消
lt_trafficSet_stcMonthAvailableTraffic		= 月可用流量
lt_trafficSet_btnChangeMonthAvalibleTraffic = 修改
lt_trafficSet_stcPeriodAvalibleTraffic		= 周期可用流量
lt_trafficSet_btnChangePeroidAvalibleTraffic= 修改
lt_trafficSet_btnSave						= 保存
TrafficCalLable							= 校准流量
TrafficCalTile							= 流量校准
MonthAvalibleTrafficSetting				= 月可用流量设置
MonthAvalibleTraffic					= 月可用流量
PeriodAvalibleTrafficSetting				= 周期可用流量设置
PeriodAvalibleTraffic					= 周期可用流量
MonthLimitTrafficSetting				= 月限制流量设置
MonthLimitTraffic						= 月限制流量
PeriodLimitTrafficSetting				= 周期限制流量设置
PeriodLimitTraffic						= 周期限制流量
lTimeFormatError						= 时间格式错误(时间格式2014/6/13)或者起始时间错误
lt_trafficSet_stcUnlimit				= 无限制周期
lt_trafficSet_stcUnlimitPeriodUsedTaffic = 无限制周期已用流量
lt_trafficSet_stcUnlimitPeriodAvailableTraffic = 无限制周期可用流量
lt_trafficSet_stcUnlimitPeriodTotalTaffic  = 无限制周期限制流量
lt_trafficSet_btnCalUnlimitPeriodTraffic = 校准
lt_trafficSet_btnChangeUnlimitPeriodAvalibleTraffic = 修改
lt_trafficSet_btnChangeUnlimitPeriodTotalTraffic = 修改
lt_trafficSet_stcDisconnectNetworkLabel = 达到流量限制时是否关闭网络连接
lt_trafficSet_stcNoAction = 无动作
lt_trafficSet_stcDisconnect = 关闭网络
PeriodTimeRangeSetError = 周期时间设置必须包含当前日期

#power off router
mPowerOffRouter = 关闭路由器
lPowerOffRouter = 请点击下面按钮关闭路由器

# SIM卡管理
pSimCardSelection = SIM卡选择:
lSim1Card = SIM卡1
lSim2Card = SIM卡2
lSimCardSwitchConfirm = 确定要切换到{0}吗？这可能会导致暂时断网。
lSimCardSwitchSuccess = SIM卡切换成功。
lSimCardSwitchFailed = SIM卡切换失败，请重试。
lSuccess = 成功
lError = 错误
h1PowerOffRouter = 关闭路由器
btPowerOffRouter = 关闭路由器
labelPowerOffRouter = 路由器正在关闭......
lQueryPowerOffRouter = 确定要关闭路由器？
btnPowerOffOK = 确定
btnModalCancle = 取消

mUssd = USSD
lt_ussd_stcTitle = USSD业务
lt_ussd_stcUssdServiceNumber = 请输入USSD业务号发起拨号
lt_ussd_btnDial = 拨号
lt_ussd_stcRecvTitle = 短消息内容
lt_ussd_stcResponseTitle = 请输入内容
lt_ussd_btnCancel = 取消
lt_ussd_btnSend = 发送
UssdServiceNumberEmpty = USSD业务号不能为空