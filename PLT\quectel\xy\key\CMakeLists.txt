message(STATUS "cmake config ${CMAKE_CURRENT_SOURCE_DIR}")
# set package target name
set(target xy-key)
# set package target object name
set(target_name ${target}_obj)

list(APPEND PACKAGE_SRC_FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/src/xy_key.c
)

add_library(${target_name} OBJECT ${PACKAGE_SRC_FILES})
add_library(${target} $<TARGET_OBJECTS:${target_name}>)

# target include dir
target_include_directories(${target_name} PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/inc)

target_compile_options(${target_name} PRIVATE --diag_suppress=61 --diag_suppress=188)
creat_prebuild_file_pack(${target} ${target_name})
